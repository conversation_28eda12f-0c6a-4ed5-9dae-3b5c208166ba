// 🧠 Procedural Arena Simulation with Dynamic AI Swarming
class ProceduralArena {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.width = this.canvas.width;
        this.height = this.canvas.height;
        
        // Game state
        this.isPaused = false;
        this.frameCount = 0;
        this.lastTime = 0;
        this.fps = 0;
        
        // Entities
        this.player = {
            x: this.width / 2,
            y: this.height / 2,
            radius: 15,
            speed: 3,
            color: '#4CAF50'
        };
        
        this.egg = {
            x: this.width / 2 + 50,
            y: this.height / 2,
            radius: 8,
            angle: 0,
            orbitRadius: 60,
            color: '#FFD700',
            spawnCooldown: 0
        };
        
        this.mobs = [];
        this.walls = [];

        // Spatial partitioning for performance optimization
        this.gridSize = 40; // Grid cell size for spatial partitioning
        this.spatialGrid = new Map();

        // Input handling
        this.keys = {};
        this.setupInput();
        
        // Generate procedural walls
        this.generateBumpyWalls();
        
        // Setup controls
        this.setupControls();
        
        // Start game loop
        this.gameLoop();
    }
    
    // 🔧 Procedural Boundary Generation (Bumpy Walls)
    generateBumpyWalls() {
        this.walls = [];
        const margin = 50;
        const frequency = 3; // Sine wave frequency
        const amplitude = 15; // Bump amplitude
        
        // Top wall
        this.createBumpyWall(
            margin, margin,
            this.width - margin, margin,
            frequency, amplitude
        );
        
        // Right wall
        this.createBumpyWall(
            this.width - margin, margin,
            this.width - margin, this.height - margin,
            frequency, amplitude
        );
        
        // Bottom wall
        this.createBumpyWall(
            this.width - margin, this.height - margin,
            margin, this.height - margin,
            frequency, amplitude
        );
        
        // Left wall
        this.createBumpyWall(
            margin, this.height - margin,
            margin, margin,
            frequency, amplitude
        );
    }
    
    createBumpyWall(x1, y1, x2, y2, frequency, amplitude) {
        const segments = 50;
        const wall = [];
        
        for (let i = 0; i <= segments; i++) {
            const t = i / segments;
            const x = x1 + (x2 - x1) * t;
            const y = y1 + (y2 - y1) * t;
            
            // Calculate perpendicular direction for bumps
            const dx = x2 - x1;
            const dy = y2 - y1;
            const length = Math.sqrt(dx * dx + dy * dy);
            const perpX = -dy / length;
            const perpY = dx / length;
            
            // Apply sine-based perturbation
            const bump = Math.sin(t * Math.PI * frequency) * amplitude;
            
            wall.push({
                x: x + perpX * bump,
                y: y + perpY * bump
            });
        }
        
        this.walls.push(wall);
    }

    // 🎮 Input System
    setupInput() {
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
        });

        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });
    }

    // 🎯 Collision Detection System
    checkCircleWallCollision(x, y, radius) {
        for (const wall of this.walls) {
            for (let i = 0; i < wall.length - 1; i++) {
                const p1 = wall[i];
                const p2 = wall[i + 1];

                const dist = this.pointToLineDistance(x, y, p1.x, p1.y, p2.x, p2.y);
                if (dist < radius) {
                    return true;
                }
            }
        }
        return false;
    }

    pointToLineDistance(px, py, x1, y1, x2, y2) {
        const A = px - x1;
        const B = py - y1;
        const C = x2 - x1;
        const D = y2 - y1;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        let param = -1;

        if (lenSq !== 0) {
            param = dot / lenSq;
        }

        let xx, yy;

        if (param < 0) {
            xx = x1;
            yy = y1;
        } else if (param > 1) {
            xx = x2;
            yy = y2;
        } else {
            xx = x1 + param * C;
            yy = y1 + param * D;
        }

        const dx = px - xx;
        const dy = py - yy;
        return Math.sqrt(dx * dx + dy * dy);
    }

    checkCircleCircleCollision(x1, y1, r1, x2, y2, r2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        const distance = Math.sqrt(dx * dx + dy * dy);
        return distance < (r1 + r2);
    }

    // 🗂️ Spatial Partitioning System for Performance
    getGridKey(x, y) {
        const gx = Math.floor(x / this.gridSize);
        const gy = Math.floor(y / this.gridSize);
        return `${gx},${gy}`;
    }

    updateSpatialGrid() {
        this.spatialGrid.clear();

        // Add all mobs to spatial grid
        for (let i = 0; i < this.mobs.length; i++) {
            const mob = this.mobs[i];
            const key = this.getGridKey(mob.x, mob.y);

            if (!this.spatialGrid.has(key)) {
                this.spatialGrid.set(key, []);
            }
            this.spatialGrid.get(key).push(i);
        }
    }

    getNearbyMobs(x, y, radius) {
        const nearby = [];
        const cellRadius = Math.ceil(radius / this.gridSize);
        const centerGx = Math.floor(x / this.gridSize);
        const centerGy = Math.floor(y / this.gridSize);

        // Check surrounding grid cells
        for (let dx = -cellRadius; dx <= cellRadius; dx++) {
            for (let dy = -cellRadius; dy <= cellRadius; dy++) {
                const key = `${centerGx + dx},${centerGy + dy}`;
                const mobIndices = this.spatialGrid.get(key);

                if (mobIndices) {
                    for (const mobIndex of mobIndices) {
                        const mob = this.mobs[mobIndex];
                        const dist = Math.sqrt((mob.x - x) ** 2 + (mob.y - y) ** 2);
                        if (dist <= radius) {
                            nearby.push(mob);
                        }
                    }
                }
            }
        }

        return nearby;
    }

    getDensityAtPosition(x, y, radius = 60) {
        return this.getNearbyMobs(x, y, radius).length;
    }

    // 🚀 Movement System with Collision Resolution
    updatePlayer() {
        let dx = 0;
        let dy = 0;

        // Input handling
        if (this.keys['KeyW'] || this.keys['ArrowUp']) dy -= this.player.speed;
        if (this.keys['KeyS'] || this.keys['ArrowDown']) dy += this.player.speed;
        if (this.keys['KeyA'] || this.keys['ArrowLeft']) dx -= this.player.speed;
        if (this.keys['KeyD'] || this.keys['ArrowRight']) dx += this.player.speed;

        // Split movement into X and Y components for wall sliding
        const newX = this.player.x + dx;
        const newY = this.player.y + dy;

        // Check X movement
        if (!this.checkCircleWallCollision(newX, this.player.y, this.player.radius)) {
            this.player.x = newX;
        }

        // Check Y movement
        if (!this.checkCircleWallCollision(this.player.x, newY, this.player.radius)) {
            this.player.y = newY;
        }
    }

    // 🥚 Orbiting Egg with Delayed Easing
    updateEgg() {
        this.egg.angle += 0.02; // Orbit speed

        // Calculate target position
        const targetX = this.player.x + Math.cos(this.egg.angle) * this.egg.orbitRadius;
        const targetY = this.player.y + Math.sin(this.egg.angle) * this.egg.orbitRadius;

        // Apply delayed easing for trailing effect
        this.egg.x += (targetX - this.egg.x) * 0.08;
        this.egg.y += (targetY - this.egg.y) * 0.08;

        // Update spawn cooldown
        if (this.egg.spawnCooldown > 0) {
            this.egg.spawnCooldown--;
        }
    }

    // 🤖 Enhanced Anti-Clustering Spawn System
    spawnMob(forceSpawn = false) {
        if (!forceSpawn && this.egg.spawnCooldown > 0) return;

        const mobRadius = 8;
        const candidates = [];

        // Strategy 1: Find low-density areas away from clusters
        const searchRadius = 150;
        const angleSteps = 16;
        const radiusSteps = 6;

        for (let r = 1; r <= radiusSteps; r++) {
            const currentRadius = (r / radiusSteps) * searchRadius;

            for (let a = 0; a < angleSteps; a++) {
                const angle = (a / angleSteps) * Math.PI * 2;
                const testX = this.egg.x + Math.cos(angle) * currentRadius;
                const testY = this.egg.y + Math.sin(angle) * currentRadius;

                // Check if position is valid
                if (this.isValidSpawnPosition(testX, testY, mobRadius)) {
                    const density = this.getDensityAtPosition(testX, testY, 50);
                    const distanceFromEgg = Math.sqrt((testX - this.egg.x) ** 2 + (testY - this.egg.y) ** 2);

                    // Score based on low density and reasonable distance
                    const score = (1 / (density + 1)) * (1 / (distanceFromEgg / 50 + 1));

                    candidates.push({
                        x: testX,
                        y: testY,
                        score: score,
                        density: density
                    });
                }
            }
        }

        // Strategy 2: If no good candidates, use dispersed random sampling
        if (candidates.length === 0) {
            for (let attempt = 0; attempt < 50; attempt++) {
                const angle = Math.random() * Math.PI * 2;
                const distance = 80 + Math.random() * 100; // Force distance from egg
                const testX = this.egg.x + Math.cos(angle) * distance;
                const testY = this.egg.y + Math.sin(angle) * distance;

                if (this.isValidSpawnPosition(testX, testY, mobRadius)) {
                    const density = this.getDensityAtPosition(testX, testY, 40);
                    if (density < 3) { // Only spawn in low-density areas
                        candidates.push({
                            x: testX,
                            y: testY,
                            score: 1 / (density + 1),
                            density: density
                        });
                    }
                }
            }
        }

        // Select best candidate (lowest density, highest score)
        if (candidates.length > 0) {
            candidates.sort((a, b) => b.score - a.score);
            const best = candidates[0];

            // Only spawn if density is reasonable
            if (best.density < 5 || forceSpawn) {
                this.createMob(best.x, best.y);
                this.egg.spawnCooldown = 180; // Longer cooldown for better spacing
                return true;
            }
        }

        return false; // Failed to find suitable location
    }

    isValidSpawnPosition(x, y, radius) {
        // Check bounds
        if (x - radius < 0 || x + radius > this.width ||
            y - radius < 0 || y + radius > this.height) {
            return false;
        }

        // Check wall collision
        if (this.checkCircleWallCollision(x, y, radius)) return false;

        // Check player collision with buffer
        const playerBuffer = 25;
        if (this.checkCircleCircleCollision(x, y, radius + playerBuffer,
            this.player.x, this.player.y, this.player.radius)) {
            return false;
        }

        // Use spatial grid for efficient mob collision checking
        const nearbyMobs = this.getNearbyMobs(x, y, radius + 15); // Small buffer
        for (const mob of nearbyMobs) {
            if (this.checkCircleCircleCollision(x, y, radius, mob.x, mob.y, mob.radius)) {
                return false;
            }
        }

        return true;
    }

    // Legacy method for backward compatibility
    isPositionBlocked(x, y, radius) {
        return !this.isValidSpawnPosition(x, y, radius);
    }

    createMob(x, y) {
        this.mobs.push({
            x: x,
            y: y,
            radius: 8,
            speed: 1.5,
            color: `hsl(${Math.random() * 60 + 300}, 70%, 60%)`, // Purple-pink range
            cloneCooldown: Math.floor(Math.random() * 300 + 180), // 3-8 seconds
            vx: (Math.random() - 0.5) * 2,
            vy: (Math.random() - 0.5) * 2
        });
    }

    // 🦠 Enhanced Swarm AI with Anti-Clustering
    updateMobs() {
        // Update spatial grid once per frame
        this.updateSpatialGrid();

        for (let i = 0; i < this.mobs.length; i++) {
            const mob = this.mobs[i];

            // Update clone cooldown
            if (mob.cloneCooldown > 0) {
                mob.cloneCooldown--;
            }

            // Anti-clustering proliferation logic
            if (mob.cloneCooldown <= 0) {
                const localDensity = this.getDensityAtPosition(mob.x, mob.y, 60);
                const globalDensity = this.mobs.length;

                // Only proliferate if local density is moderate and global population is low
                if (localDensity >= 2 && localDensity <= 4 && globalDensity < 30) {
                    if (this.attemptAntiClusterClone(mob)) {
                        mob.cloneCooldown = Math.floor(Math.random() * 900 + 600); // Longer cooldown
                    }
                }
            }

            // Enhanced movement with cluster avoidance
            const nearbyMobs = this.getNearbyMobs(mob.x, mob.y, 40);
            let avoidanceX = 0;
            let avoidanceY = 0;

            // Calculate avoidance vector from nearby mobs
            for (const nearby of nearbyMobs) {
                if (nearby !== mob) {
                    const dx = mob.x - nearby.x;
                    const dy = mob.y - nearby.y;
                    const dist = Math.sqrt(dx * dx + dy * dy);
                    if (dist > 0 && dist < 30) {
                        const force = (30 - dist) / 30;
                        avoidanceX += (dx / dist) * force * 0.3;
                        avoidanceY += (dy / dist) * force * 0.3;
                    }
                }
            }

            // Apply movement with avoidance
            mob.vx += avoidanceX;
            mob.vy += avoidanceY;
            mob.x += mob.vx;
            mob.y += mob.vy;

            // Bounce off walls with dampening
            if (this.checkCircleWallCollision(mob.x, mob.y, mob.radius)) {
                mob.vx *= -0.6;
                mob.vy *= -0.6;
                mob.x -= mob.vx * 3;
                mob.y -= mob.vy * 3;
            }

            // Occasional random movement
            if (Math.random() < 0.015) {
                mob.vx += (Math.random() - 0.5) * 0.4;
                mob.vy += (Math.random() - 0.5) * 0.4;
            }

            // Velocity dampening and speed limiting
            mob.vx *= 0.98;
            mob.vy *= 0.98;

            const speed = Math.sqrt(mob.vx * mob.vx + mob.vy * mob.vy);
            if (speed > mob.speed) {
                mob.vx = (mob.vx / speed) * mob.speed;
                mob.vy = (mob.vy / speed) * mob.speed;
            }
        }

        // Optimized collision resolution
        this.resolveMobCollisions();
    }

    attemptAntiClusterClone(parentMob) {
        const candidates = [];
        const minSpawnDistance = 40; // Force distance from parent
        const maxSpawnDistance = 100;

        // Try to spawn away from clusters
        for (let attempt = 0; attempt < 12; attempt++) {
            const angle = Math.random() * Math.PI * 2;
            const distance = minSpawnDistance + Math.random() * (maxSpawnDistance - minSpawnDistance);
            const x = parentMob.x + Math.cos(angle) * distance;
            const y = parentMob.y + Math.sin(angle) * distance;

            if (this.isValidSpawnPosition(x, y, 8)) {
                const density = this.getDensityAtPosition(x, y, 50);
                candidates.push({ x, y, density });
            }
        }

        if (candidates.length > 0) {
            // Choose location with lowest density
            candidates.sort((a, b) => a.density - b.density);
            const best = candidates[0];

            // Only spawn if density is low enough
            if (best.density <= 2) {
                this.createMob(best.x, best.y);
                return true;
            }
        }

        return false;
    }

    // Legacy method for backward compatibility
    attemptMobClone(parentMob) {
        return this.attemptAntiClusterClone(parentMob);
    }

    // 💥 Optimized Mob Collision Resolution with Spatial Partitioning
    resolveMobCollisions() {
        // Update spatial grid first
        this.updateSpatialGrid();

        const processedPairs = new Set();

        // Only check collisions within same and adjacent grid cells
        for (let i = 0; i < this.mobs.length; i++) {
            const mob1 = this.mobs[i];
            const nearbyMobs = this.getNearbyMobs(mob1.x, mob1.y, mob1.radius * 3);

            for (const mob2 of nearbyMobs) {
                if (mob1 === mob2) continue;

                // Create unique pair identifier to avoid duplicate processing
                const mob1Index = this.mobs.indexOf(mob1);
                const mob2Index = this.mobs.indexOf(mob2);
                const pairKey = mob1Index < mob2Index ? `${mob1Index}-${mob2Index}` : `${mob2Index}-${mob1Index}`;

                if (processedPairs.has(pairKey)) continue;
                processedPairs.add(pairKey);

                const dx = mob2.x - mob1.x;
                const dy = mob2.y - mob1.y;
                const dist = Math.sqrt(dx * dx + dy * dy);
                const minDist = mob1.radius + mob2.radius;

                if (dist < minDist && dist > 0.1) { // Avoid division by zero
                    // Gentle separation instead of aggressive pushing
                    const overlap = minDist - dist;
                    const separationForce = Math.min(overlap * 0.3, 2); // Limit max separation

                    const pushX = (dx / dist) * separationForce;
                    const pushY = (dy / dist) * separationForce;

                    mob1.x -= pushX;
                    mob1.y -= pushY;
                    mob2.x += pushX;
                    mob2.y += pushY;

                    // Damped velocity exchange to prevent oscillation
                    const dampening = 0.3;
                    const tempVx = mob1.vx * dampening;
                    const tempVy = mob1.vy * dampening;
                    mob1.vx = mob1.vx * (1 - dampening) + mob2.vx * dampening;
                    mob1.vy = mob1.vy * (1 - dampening) + mob2.vy * dampening;
                    mob2.vx = mob2.vx * (1 - dampening) + tempVx;
                    mob2.vy = mob2.vy * (1 - dampening) + tempVy;
                }
            }
        }
    }

    // 🎨 Rendering System
    render() {
        // Clear canvas
        this.ctx.fillStyle = '#000';
        this.ctx.fillRect(0, 0, this.width, this.height);

        // Render bumpy walls
        this.ctx.strokeStyle = '#444';
        this.ctx.lineWidth = 3;
        this.ctx.setLineDash([]);

        for (const wall of this.walls) {
            this.ctx.beginPath();
            this.ctx.moveTo(wall[0].x, wall[0].y);
            for (let i = 1; i < wall.length; i++) {
                this.ctx.lineTo(wall[i].x, wall[i].y);
            }
            this.ctx.stroke();
        }

        // Render spawn preview (dashed line from egg)
        if (this.egg.spawnCooldown <= 0) {
            this.ctx.strokeStyle = '#FFD700';
            this.ctx.lineWidth = 2;
            this.ctx.setLineDash([5, 5]);
            this.ctx.beginPath();
            this.ctx.arc(this.egg.x, this.egg.y, 20, 0, Math.PI * 2);
            this.ctx.stroke();
        }

        // Render mobs
        for (const mob of this.mobs) {
            this.ctx.fillStyle = mob.color;
            this.ctx.beginPath();
            this.ctx.arc(mob.x, mob.y, mob.radius, 0, Math.PI * 2);
            this.ctx.fill();

            // Mob outline
            this.ctx.strokeStyle = '#fff';
            this.ctx.lineWidth = 1;
            this.ctx.setLineDash([]);
            this.ctx.stroke();
        }

        // Render player
        this.ctx.fillStyle = this.player.color;
        this.ctx.beginPath();
        this.ctx.arc(this.player.x, this.player.y, this.player.radius, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.strokeStyle = '#fff';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([]);
        this.ctx.stroke();

        // Render orbiting egg
        this.ctx.fillStyle = this.egg.color;
        this.ctx.beginPath();
        this.ctx.arc(this.egg.x, this.egg.y, this.egg.radius, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.strokeStyle = '#fff';
        this.ctx.lineWidth = 1;
        this.ctx.stroke();

        // Orbit trail
        this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.3)';
        this.ctx.lineWidth = 1;
        this.ctx.setLineDash([2, 2]);
        this.ctx.beginPath();
        this.ctx.arc(this.player.x, this.player.y, this.egg.orbitRadius, 0, Math.PI * 2);
        this.ctx.stroke();
    }

    // 🔄 Game Loop
    gameLoop(currentTime = 0) {
        // Calculate FPS
        if (currentTime - this.lastTime >= 1000) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.lastTime = currentTime;
        }
        this.frameCount++;

        if (!this.isPaused) {
            // Update systems
            this.updatePlayer();
            this.updateEgg();
            this.updateMobs();

            // Intelligent auto-spawning with density awareness
            if (this.frameCount % 180 === 0 && this.mobs.length < 40) { // Every 3 seconds
                const eggDensity = this.getDensityAtPosition(this.egg.x, this.egg.y, 80);
                if (eggDensity < 6) { // Only spawn if area isn't too crowded
                    this.spawnMob();
                }
            }
        }

        // Render
        this.render();
        this.updateStats();

        requestAnimationFrame((time) => this.gameLoop(time));
    }

    // 📊 Stats Update
    updateStats() {
        const stats = document.getElementById('stats');
        stats.textContent = `Mobs: ${this.mobs.length} | FPS: ${this.fps} | ` +
            `Player: (${Math.round(this.player.x)}, ${Math.round(this.player.y)}) | ` +
            `Egg: (${Math.round(this.egg.x)}, ${Math.round(this.egg.y)})`;
    }

    // 🎛️ Control Setup
    setupControls() {
        document.getElementById('resetBtn').addEventListener('click', () => {
            this.reset();
        });

        document.getElementById('pauseBtn').addEventListener('click', () => {
            this.isPaused = !this.isPaused;
            document.getElementById('pauseBtn').textContent = this.isPaused ? 'Resume' : 'Pause';
        });

        document.getElementById('spawnMobBtn').addEventListener('click', () => {
            this.spawnMob(true);
        });
    }

    reset() {
        this.mobs = [];
        this.player.x = this.width / 2;
        this.player.y = this.height / 2;
        this.egg.x = this.width / 2 + 50;
        this.egg.y = this.height / 2;
        this.egg.angle = 0;
        this.egg.spawnCooldown = 0;
        this.generateBumpyWalls();
    }
}

// 🚀 Initialize the simulation
document.addEventListener('DOMContentLoaded', () => {
    new ProceduralArena('gameCanvas');
});
