// 🧠 Procedural Arena Simulation with Dynamic AI Swarming
class ProceduralArena {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.width = this.canvas.width;
        this.height = this.canvas.height;
        
        // Game state
        this.isPaused = false;
        this.frameCount = 0;
        this.lastTime = 0;
        this.fps = 0;
        
        // Entities
        this.player = {
            x: this.width / 2,
            y: this.height / 2,
            radius: 15,
            speed: 3,
            color: '#4CAF50'
        };
        
        this.egg = {
            x: this.width / 2 + 50,
            y: this.height / 2,
            radius: 8,
            angle: 0,
            orbitRadius: 60,
            color: '#FFD700',
            spawnCooldown: 0
        };
        
        this.mobs = [];
        this.walls = [];
        
        // Input handling
        this.keys = {};
        this.setupInput();
        
        // Generate procedural walls
        this.generateBumpyWalls();
        
        // Setup controls
        this.setupControls();
        
        // Start game loop
        this.gameLoop();
    }
    
    // 🔧 Procedural Boundary Generation (Bumpy Walls)
    generateBumpyWalls() {
        this.walls = [];
        const margin = 50;
        const frequency = 3; // Sine wave frequency
        const amplitude = 15; // Bump amplitude
        
        // Top wall
        this.createBumpyWall(
            margin, margin,
            this.width - margin, margin,
            frequency, amplitude
        );
        
        // Right wall
        this.createBumpyWall(
            this.width - margin, margin,
            this.width - margin, this.height - margin,
            frequency, amplitude
        );
        
        // Bottom wall
        this.createBumpyWall(
            this.width - margin, this.height - margin,
            margin, this.height - margin,
            frequency, amplitude
        );
        
        // Left wall
        this.createBumpyWall(
            margin, this.height - margin,
            margin, margin,
            frequency, amplitude
        );
    }
    
    createBumpyWall(x1, y1, x2, y2, frequency, amplitude) {
        const segments = 50;
        const wall = [];
        
        for (let i = 0; i <= segments; i++) {
            const t = i / segments;
            const x = x1 + (x2 - x1) * t;
            const y = y1 + (y2 - y1) * t;
            
            // Calculate perpendicular direction for bumps
            const dx = x2 - x1;
            const dy = y2 - y1;
            const length = Math.sqrt(dx * dx + dy * dy);
            const perpX = -dy / length;
            const perpY = dx / length;
            
            // Apply sine-based perturbation
            const bump = Math.sin(t * Math.PI * frequency) * amplitude;
            
            wall.push({
                x: x + perpX * bump,
                y: y + perpY * bump
            });
        }
        
        this.walls.push(wall);
    }

    // 🎮 Input System
    setupInput() {
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
        });

        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });
    }

    // 🎯 Collision Detection System
    checkCircleWallCollision(x, y, radius) {
        for (const wall of this.walls) {
            for (let i = 0; i < wall.length - 1; i++) {
                const p1 = wall[i];
                const p2 = wall[i + 1];

                const dist = this.pointToLineDistance(x, y, p1.x, p1.y, p2.x, p2.y);
                if (dist < radius) {
                    return true;
                }
            }
        }
        return false;
    }

    pointToLineDistance(px, py, x1, y1, x2, y2) {
        const A = px - x1;
        const B = py - y1;
        const C = x2 - x1;
        const D = y2 - y1;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        let param = -1;

        if (lenSq !== 0) {
            param = dot / lenSq;
        }

        let xx, yy;

        if (param < 0) {
            xx = x1;
            yy = y1;
        } else if (param > 1) {
            xx = x2;
            yy = y2;
        } else {
            xx = x1 + param * C;
            yy = y1 + param * D;
        }

        const dx = px - xx;
        const dy = py - yy;
        return Math.sqrt(dx * dx + dy * dy);
    }

    checkCircleCircleCollision(x1, y1, r1, x2, y2, r2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        const distance = Math.sqrt(dx * dx + dy * dy);
        return distance < (r1 + r2);
    }

    // 🚀 Movement System with Collision Resolution
    updatePlayer() {
        let dx = 0;
        let dy = 0;

        // Input handling
        if (this.keys['KeyW'] || this.keys['ArrowUp']) dy -= this.player.speed;
        if (this.keys['KeyS'] || this.keys['ArrowDown']) dy += this.player.speed;
        if (this.keys['KeyA'] || this.keys['ArrowLeft']) dx -= this.player.speed;
        if (this.keys['KeyD'] || this.keys['ArrowRight']) dx += this.player.speed;

        // Split movement into X and Y components for wall sliding
        const newX = this.player.x + dx;
        const newY = this.player.y + dy;

        // Check X movement
        if (!this.checkCircleWallCollision(newX, this.player.y, this.player.radius)) {
            this.player.x = newX;
        }

        // Check Y movement
        if (!this.checkCircleWallCollision(this.player.x, newY, this.player.radius)) {
            this.player.y = newY;
        }
    }

    // 🥚 Orbiting Egg with Delayed Easing
    updateEgg() {
        this.egg.angle += 0.02; // Orbit speed

        // Calculate target position
        const targetX = this.player.x + Math.cos(this.egg.angle) * this.egg.orbitRadius;
        const targetY = this.player.y + Math.sin(this.egg.angle) * this.egg.orbitRadius;

        // Apply delayed easing for trailing effect
        this.egg.x += (targetX - this.egg.x) * 0.08;
        this.egg.y += (targetY - this.egg.y) * 0.08;

        // Update spawn cooldown
        if (this.egg.spawnCooldown > 0) {
            this.egg.spawnCooldown--;
        }
    }

    // 🤖 Intelligent Mob Spawning with Spatial Awareness
    spawnMob(forceSpawn = false) {
        if (!forceSpawn && this.egg.spawnCooldown > 0) return;

        let spawnX = this.egg.x;
        let spawnY = this.egg.y;
        const mobRadius = 8;

        // Stage 1: Try egg position
        if (!this.isPositionBlocked(spawnX, spawnY, mobRadius)) {
            this.createMob(spawnX, spawnY);
            this.egg.spawnCooldown = 120; // 2 seconds at 60fps
            return;
        }

        // Stage 2: BFS for nearest valid position
        const gridSize = 20;
        const startGridX = Math.floor(spawnX / gridSize);
        const startGridY = Math.floor(spawnY / gridSize);

        const visited = new Set();
        const queue = [[startGridX, startGridY, 0]]; // x, y, distance

        while (queue.length > 0) {
            const [gx, gy, dist] = queue.shift();
            const key = `${gx},${gy}`;

            if (visited.has(key)) continue;
            visited.add(key);

            const worldX = gx * gridSize + gridSize / 2;
            const worldY = gy * gridSize + gridSize / 2;

            // Check bounds
            if (worldX < 0 || worldX >= this.width || worldY < 0 || worldY >= this.height) {
                continue;
            }

            if (!this.isPositionBlocked(worldX, worldY, mobRadius)) {
                this.createMob(worldX, worldY);
                this.egg.spawnCooldown = 120;
                return;
            }

            // Add neighbors (BFS expansion)
            if (dist < 10) { // Limit search radius
                for (const [dx, dy] of [[-1,0], [1,0], [0,-1], [0,1], [-1,-1], [-1,1], [1,-1], [1,1]]) {
                    queue.push([gx + dx, gy + dy, dist + 1]);
                }
            }
        }
    }

    isPositionBlocked(x, y, radius) {
        // Check wall collision
        if (this.checkCircleWallCollision(x, y, radius)) return true;

        // Check player collision
        if (this.checkCircleCircleCollision(x, y, radius, this.player.x, this.player.y, this.player.radius)) {
            return true;
        }

        // Check mob collisions
        for (const mob of this.mobs) {
            if (this.checkCircleCircleCollision(x, y, radius, mob.x, mob.y, mob.radius)) {
                return true;
            }
        }

        return false;
    }

    createMob(x, y) {
        this.mobs.push({
            x: x,
            y: y,
            radius: 8,
            speed: 1.5,
            color: `hsl(${Math.random() * 60 + 300}, 70%, 60%)`, // Purple-pink range
            cloneCooldown: Math.floor(Math.random() * 300 + 180), // 3-8 seconds
            vx: (Math.random() - 0.5) * 2,
            vy: (Math.random() - 0.5) * 2
        });
    }

    // 🦠 Swarm-Style AI Proliferation (Mob Cloning)
    updateMobs() {
        for (let i = 0; i < this.mobs.length; i++) {
            const mob = this.mobs[i];

            // Update clone cooldown
            if (mob.cloneCooldown > 0) {
                mob.cloneCooldown--;
            }

            // Check for proliferation
            if (mob.cloneCooldown <= 0) {
                const nearbyMobs = this.getMobsInRadius(mob.x, mob.y, 80);
                if (nearbyMobs.length >= 3) { // Density threshold
                    this.attemptMobClone(mob);
                    mob.cloneCooldown = Math.floor(Math.random() * 600 + 300); // Reset cooldown
                }
            }

            // Simple wandering movement
            mob.x += mob.vx;
            mob.y += mob.vy;

            // Bounce off walls
            if (this.checkCircleWallCollision(mob.x, mob.y, mob.radius)) {
                mob.vx *= -0.8;
                mob.vy *= -0.8;
                // Move back to valid position
                mob.x -= mob.vx * 2;
                mob.y -= mob.vy * 2;
            }

            // Random direction changes
            if (Math.random() < 0.02) {
                mob.vx += (Math.random() - 0.5) * 0.5;
                mob.vy += (Math.random() - 0.5) * 0.5;

                // Limit speed
                const speed = Math.sqrt(mob.vx * mob.vx + mob.vy * mob.vy);
                if (speed > mob.speed) {
                    mob.vx = (mob.vx / speed) * mob.speed;
                    mob.vy = (mob.vy / speed) * mob.speed;
                }
            }
        }

        // Mob-to-mob collision resolution
        this.resolveMobCollisions();
    }

    getMobsInRadius(x, y, radius) {
        return this.mobs.filter(mob => {
            const dx = mob.x - x;
            const dy = mob.y - y;
            return Math.sqrt(dx * dx + dy * dy) <= radius;
        });
    }

    attemptMobClone(parentMob) {
        const attempts = 8;
        const spawnRadius = 25;

        for (let i = 0; i < attempts; i++) {
            const angle = (i / attempts) * Math.PI * 2;
            const x = parentMob.x + Math.cos(angle) * spawnRadius;
            const y = parentMob.y + Math.sin(angle) * spawnRadius;

            if (!this.isPositionBlocked(x, y, 8)) {
                this.createMob(x, y);
                return;
            }
        }
    }

    // 💥 Mob-to-Mob Collision Resolution
    resolveMobCollisions() {
        for (let i = 0; i < this.mobs.length; i++) {
            for (let j = i + 1; j < this.mobs.length; j++) {
                const mob1 = this.mobs[i];
                const mob2 = this.mobs[j];

                const dx = mob2.x - mob1.x;
                const dy = mob2.y - mob1.y;
                const dist = Math.sqrt(dx * dx + dy * dy);
                const minDist = mob1.radius + mob2.radius;

                if (dist < minDist && dist > 0) {
                    // Elastic repulsion
                    const push = (dx / dist) * (minDist - dist) * 0.5;
                    const pushY = (dy / dist) * (minDist - dist) * 0.5;

                    mob1.x -= push;
                    mob1.y -= pushY;
                    mob2.x += push;
                    mob2.y += pushY;

                    // Add some velocity exchange
                    const tempVx = mob1.vx;
                    const tempVy = mob1.vy;
                    mob1.vx = mob2.vx * 0.5 + mob1.vx * 0.5;
                    mob1.vy = mob2.vy * 0.5 + mob1.vy * 0.5;
                    mob2.vx = tempVx * 0.5 + mob2.vx * 0.5;
                    mob2.vy = tempVy * 0.5 + mob2.vy * 0.5;
                }
            }
        }
    }

    // 🎨 Rendering System
    render() {
        // Clear canvas
        this.ctx.fillStyle = '#000';
        this.ctx.fillRect(0, 0, this.width, this.height);

        // Render bumpy walls
        this.ctx.strokeStyle = '#444';
        this.ctx.lineWidth = 3;
        this.ctx.setLineDash([]);

        for (const wall of this.walls) {
            this.ctx.beginPath();
            this.ctx.moveTo(wall[0].x, wall[0].y);
            for (let i = 1; i < wall.length; i++) {
                this.ctx.lineTo(wall[i].x, wall[i].y);
            }
            this.ctx.stroke();
        }

        // Render spawn preview (dashed line from egg)
        if (this.egg.spawnCooldown <= 0) {
            this.ctx.strokeStyle = '#FFD700';
            this.ctx.lineWidth = 2;
            this.ctx.setLineDash([5, 5]);
            this.ctx.beginPath();
            this.ctx.arc(this.egg.x, this.egg.y, 20, 0, Math.PI * 2);
            this.ctx.stroke();
        }

        // Render mobs
        for (const mob of this.mobs) {
            this.ctx.fillStyle = mob.color;
            this.ctx.beginPath();
            this.ctx.arc(mob.x, mob.y, mob.radius, 0, Math.PI * 2);
            this.ctx.fill();

            // Mob outline
            this.ctx.strokeStyle = '#fff';
            this.ctx.lineWidth = 1;
            this.ctx.setLineDash([]);
            this.ctx.stroke();
        }

        // Render player
        this.ctx.fillStyle = this.player.color;
        this.ctx.beginPath();
        this.ctx.arc(this.player.x, this.player.y, this.player.radius, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.strokeStyle = '#fff';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([]);
        this.ctx.stroke();

        // Render orbiting egg
        this.ctx.fillStyle = this.egg.color;
        this.ctx.beginPath();
        this.ctx.arc(this.egg.x, this.egg.y, this.egg.radius, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.strokeStyle = '#fff';
        this.ctx.lineWidth = 1;
        this.ctx.stroke();

        // Orbit trail
        this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.3)';
        this.ctx.lineWidth = 1;
        this.ctx.setLineDash([2, 2]);
        this.ctx.beginPath();
        this.ctx.arc(this.player.x, this.player.y, this.egg.orbitRadius, 0, Math.PI * 2);
        this.ctx.stroke();
    }

    // 🔄 Game Loop
    gameLoop(currentTime = 0) {
        // Calculate FPS
        if (currentTime - this.lastTime >= 1000) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.lastTime = currentTime;
        }
        this.frameCount++;

        if (!this.isPaused) {
            // Update systems
            this.updatePlayer();
            this.updateEgg();
            this.updateMobs();

            // Auto-spawn mobs occasionally
            if (Math.random() < 0.005 && this.mobs.length < 50) {
                this.spawnMob();
            }
        }

        // Render
        this.render();
        this.updateStats();

        requestAnimationFrame((time) => this.gameLoop(time));
    }

    // 📊 Stats Update
    updateStats() {
        const stats = document.getElementById('stats');
        stats.textContent = `Mobs: ${this.mobs.length} | FPS: ${this.fps} | ` +
            `Player: (${Math.round(this.player.x)}, ${Math.round(this.player.y)}) | ` +
            `Egg: (${Math.round(this.egg.x)}, ${Math.round(this.egg.y)})`;
    }

    // 🎛️ Control Setup
    setupControls() {
        document.getElementById('resetBtn').addEventListener('click', () => {
            this.reset();
        });

        document.getElementById('pauseBtn').addEventListener('click', () => {
            this.isPaused = !this.isPaused;
            document.getElementById('pauseBtn').textContent = this.isPaused ? 'Resume' : 'Pause';
        });

        document.getElementById('spawnMobBtn').addEventListener('click', () => {
            this.spawnMob(true);
        });
    }

    reset() {
        this.mobs = [];
        this.player.x = this.width / 2;
        this.player.y = this.height / 2;
        this.egg.x = this.width / 2 + 50;
        this.egg.y = this.height / 2;
        this.egg.angle = 0;
        this.egg.spawnCooldown = 0;
        this.generateBumpyWalls();
    }
}

// 🚀 Initialize the simulation
document.addEventListener('DOMContentLoaded', () => {
    new ProceduralArena('gameCanvas');
});
