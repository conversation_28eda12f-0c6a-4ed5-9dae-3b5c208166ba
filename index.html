<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Procedural Arena Simulation</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            font-family: 'Courier New', monospace;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        h1 {
            margin-bottom: 10px;
            color: #4CAF50;
        }
        
        .info {
            margin-bottom: 20px;
            text-align: center;
            font-size: 14px;
            color: #888;
        }
        
        canvas {
            border: 2px solid #333;
            background: #000;
            cursor: crosshair;
        }
        
        .controls {
            margin-top: 20px;
            text-align: center;
        }
        
        .stats {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            border-radius: 4px;
            font-family: inherit;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>🧠 Procedural Arena with Dynamic AI Swarming</h1>
    <div class="info">
        Use WASD or Arrow Keys to move • Watch the AI mobs spawn and swarm • Egg orbits with delayed physics
    </div>
    
    <canvas id="gameCanvas" width="1000" height="700"></canvas>
    
    <div class="controls">
        <button id="resetBtn">Reset Simulation</button>
        <button id="pauseBtn">Pause</button>
        <button id="spawnMobBtn">Spawn Mob</button>
    </div>
    
    <div class="stats" id="stats">
        Mobs: 0 | FPS: 0 | Player: (0, 0) | Egg: (0, 0)
    </div>
    
    <script src="simulation.js"></script>
</body>
</html>
